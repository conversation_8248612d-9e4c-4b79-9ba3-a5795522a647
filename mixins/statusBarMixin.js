// 状态栏适配混入
export default {
  data() {
    return {
      statusBarHeight: 0,
    };
  },
  onLoad() {
    // 获取全局状态栏高度
    this.statusBarHeight = getApp().globalData.statusBarHeight || 0;
  },
  computed: {
    // 状态栏样式
    statusBarStyle() {
      return {
        paddingTop: this.statusBarHeight + "px",
      };
    },
    // 状态栏高度（rpx单位）
    statusBarHeightRpx() {
      return this.statusBarHeight * 2; // px转rpx
    },
  },
};
