import store from '../store/index.js'
//服务器地址 117.21.200.184   administrator  Zjh@20241102!
const service = "https://boshi.channce.com/boshi";
module.exports = (params) => {
	let url = params.url;
	let method = params.method ? params.method : "POST";
	let header = params.header || {};
	let data = params.data || {};
	//	请求方式 GET POST
	if (method) {
		method = method.toUpperCase(); //	小写转大写
		if (method == "POST") {
			header = {
				"content-type": "application/json",
				"parentId": "81556ac364d84cc2b77817841dab21d2",
				'ac': uni.getStorageSync('token3'),
				'pid': store.state.userInfo.userId
			}
		}
	}
	//	发起请求 加载动画
	if (!params.hideLoading) {
		uni.showLoading({
			title: ""
		})
	}
	//	发起网络请求
	uni.request({
		url: url ? service + url : service + '/api/service',
		method: method,
		header: header,
		data: data,
		dataType: "json",
		sslVerify: false,
		success: res => {
			uni.hideLoading()
			if (res.data.code && res.data.code != 2000) {
				if (res.data.code == '4000') {
					// uni.showToast({ title: '请先登录', icon: 'none' })
					// uni.clearStorage();

					// // #ifndef MP-WEIXIN
					// setTimeout(() => { uni.navigateTo({ url: `/pages_account/login` }) }, 1000)
					// // #endif
					// // #ifdef MP-WEIXIN 
					// setTimeout(() => { uni.navigateTo({ url: `/pages_account/wx_login` }) }, 1000)
					// // #endif
					// return
				} else {
					uni.showToast({ title: res.data.message, icon: 'none' })
				}
			}
			typeof params.success == "function" && params.success(res.data);
		},
		fail: err => {
			uni.showToast({
				title: res.data.message,
				icon: 'none'
			})
			typeof params.fail == "function" && params.fail(err.data);
		},
		complete: (e) => {
			typeof params.complete == "function" && params.complete(e.data);
			return;
		}
	})
}