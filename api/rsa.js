import CryptoJS from 'crypto-js/crypto-js'
export default {
	// beas() {
	//   const Base64 = require("js-base64").Base64;
	//   const exStr = Base64.decode(sessionStorage.getItem("token"));
	//   return exStr
	// },

	encrypt(data, is) {
		console.log(data);
		if (!is) {
			return JSON.stringify(data)
		}
		let srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(data));
		var key = CryptoJS.enc.Utf8.parse(uni.getStorageSync('token1'))
		var iv = CryptoJS.enc.Utf8.parse(uni.getStorageSync('token2'))
		var encrypted = CryptoJS.AES.encrypt(srcs, key, {
			iv: iv,
			mode: CryptoJS.mode.CBC,
			padding: CryptoJS.pad.ZeroPadding
		});
		return CryptoJS.enc.Hex.stringify(encrypted.ciphertext);
	}
}
