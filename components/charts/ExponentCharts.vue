<template>
  <view class="exponent-charts">
    <view class="chart-container">

      <!-- 四个指标柱状图 -->
      <view class="indicators">
        <view
          v-for="(item, index) in chartData"
          :key="index"
          class="indicator-item"
        >
          <view class="indicator-bar">
            <view class="indicator-bg"></view>
            <view
              class="indicator-value"
              :style="{ height: item.value + '%', backgroundColor: item.color }"
            ></view>
          </view>
          <view class="indicator-number">{{ item.score }}</view>
          <view class="indicator-name">{{ item.name }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "ExponentCharts",
  props: {
    /*
     * 外部可传入数据: [{ name: '爱情', score: 64, value: 64, color: '#FF40A0' }, ...]
     * 如果未传入，则使用默认示例数据
     */
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      defaultData: [
        { name: "爱情", score: 64, value: 64, color: "#FF40A0" },
        { name: "财富", score: 48, value: 48, color: "#FFC040" },
        { name: "事业", score: 57, value: 57, color: "#6040FF" },
        { name: "学习", score: 72, value: 72, color: "#40D0E0" },
      ],
    };
  },
  computed: {
    chartData() {
      return this.data && this.data.length ? this.data : this.defaultData;
    },
  },
};
</script>

<style scoped>
.exponent-charts {
  width: 100%;
  /* padding: 20rpx 0; */
}

.chart-container {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

/* 虚线 */
.dotted-line {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  border-top: 1px dashed #d9d9d9;
  z-index: 1;
}

/* 指标柱区域 */
.indicators {
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.indicator-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  height: 100%;
}

.indicator-bar {
  position: relative;
  width: 16rpx;
  height: 100rpx;
  margin-bottom: 12rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

/* 背景条 */
.indicator-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #ebedf0;
  border-radius: 100px;
}

/* 动态填充条 */
.indicator-value {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 100px;
}

/* 数值 */
.indicator-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 40rpx;
}

/* 名称 */
.indicator-name {
  margin-top: 6rpx;
  font-size: 26rpx;
  color: #666;
}
</style>