<template>
  <view class="font-bottom">
    <view class="bottom-color" :style="{ color: color, fontSize: size }">
      {{ title }}
    </view>
    <view class="underline" :style="{ backgroundColor: underline }"></view>
  </view>
</template>

<script>
export default {
  props: {
    underline: {
      type: String,
      default: "none",
    },
    color: {
      type: String,
      default: "#FFFFFF",
    },
    title: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "38rpx",
    },
  },
};
</script>

<style scoped lang="scss">
.font-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 99;

  .bottom-color {
    display: inline-block;
  }

  .underline {
    width: 100%;
    height: 8rpx;
    position: absolute;
    z-index: -1;
    bottom: 6rpx;
  }
}
</style>
