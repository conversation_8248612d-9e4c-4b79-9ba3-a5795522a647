<template>
  <view class="input-container">
    <view class="icon" style="width: 48rpx; height: 48rpx">
      <image :src="icon" style="width: 48rpx; height: 48rpx"></image>
    </view>
    <uni-easyinput
      class="input"
      :type="type"
      :value="value"
      :placeholder="placeholder"
      placeholder-style="color: #C3C5CA; font-size: 28rpx; font-family: 苹方-简 常规体; line-height: 40rpx; padding: 4rpx 0;"
      @input="onInput"
      :clearable="clearable"
    ></uni-easyinput>
    <slot name="right"></slot>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      default: "",
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请输入",
    },
    icon: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "text",
    },
  },
  data() {
    return {};
  },
  methods: {
    onInput(value) {
      console.log(value);
      this.$emit("input", value);
    },
  },
};
</script>

<style lang="scss" scoped>
.input-container {
  width: 622rpx;
  background-color: #fff;
  padding: 24rpx;
  border-radius: 24rpx;
  box-sizing: border-box;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: start;

  .input {
    flex: 1;
    border-radius: 20rpx;
    background-color: #ffffff;
    font-size: 24rpx;
  }
}
::v-deep .is-input-border {
  border: none !important;
}
::v-deep .uni-icons {
  display: none !important;
}
</style>
