<template>
  <view class="tab-list">
    <view
      v-for="(item, index) in tabList"
      :key="index"
      class="tab-item"
      :class="{ active: currentTab === item.tab }"
      @click="handleTabClick(item, index)"
    >
      <view class="tab-title">{{ item.title }}</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    tabList: { type: Array, default: [] },
    defaultTab: { type: Number, default: 1 },
  },
  data() {
    return {
      currentTab: this.defaultTab,
    };
  },
  methods: {
    handleTabClick(item, index) {
      this.currentTab = item.tab;
      this.$emit("tab-change", {
        tab: item.tab,
        index: index,
        item: item,
      });
    },
  },
  watch: {
    defaultTab: {
      handler(newVal) {
        this.currentTab = newVal;
      },
      immediate: true,
    },
  },
};
</script>

<style scoped lang="scss">
.tab-list {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 64rpx;

  .tab-item {
    position: relative;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    .tab-title {
      font-size: 28rpx;
      color: #8c8b89;
      transition:
        color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      transform: scale(1);
    }

    &::after {
      content: "";
      position: absolute;
      bottom: -8rpx;
      left: 50%;
      transform: translateX(-50%) scaleX(0);
      width: 40rpx;
      height: 4rpx;
      background: #000;
      border-radius: 2rpx;
      transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      transform-origin: center;
    }

    &.active {
      .tab-title {
        color: #000;
        font-weight: 600;
        transform: scale(1.28);
      }

      &::after {
        transform: translateX(-50%) scaleX(1);
      }
    }

    &:hover:not(.active) {
      .tab-title {
        color: #333;
        transform: scale(1.1);
      }
    }
  }
}
</style>
