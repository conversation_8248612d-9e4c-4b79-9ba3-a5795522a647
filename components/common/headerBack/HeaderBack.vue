<template>
  <view class="header-back">
    <image
      class="back-icon"
      src="/static/icon/close.png"
      @click="$emit('back')"
    ></image>
    <view class="title">{{ title }}</view>
    <view class="back-icon"></view>
  </view>
</template>

<script>
export default {
  props: {
    title: { type: String, default: "返回" },
  },
};
</script>

<style scoped lang="scss">
.header-back {
  box-sizing: border-box;
  width: 100%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .back-icon {
    width: 28rpx;
    height: 28rpx;
  }
  .title {
    font-size: 32rpx;
    color: #000;
    flex: 1;
    text-align: center;
    font-weight: bold;
  }
}
</style>
