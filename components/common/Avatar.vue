<template>
  <view class="avatar">
    <view class="avatar-img" :style="{ border: `1px dashed ${color}` }">
      <image src="/static/home/<USER>"></image>
    </view>
    <view class="avatar-center">
      <image src="/static/icon/connect.gif"></image>
    </view>
    <view class="avatar-img" :style="{ border: `1px dashed ${color}` }">
      <image src="/static/home/<USER>"></image>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    color: {
      type: String,
      default: "#FFFFFF",
    },
  },
};
</script>

<style scoped lang="scss">
.avatar {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  gap: 16rpx;
  justify-content: space-between;
  align-items: center;

  .avatar-img {
    width: 104rpx;
    height: 104rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 89.72rpx;
      height: 89.72rpx;
      border-radius: 50%;
    }
  }

  .avatar-center {
    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}
</style>
