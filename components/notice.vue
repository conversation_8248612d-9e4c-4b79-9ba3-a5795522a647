<template>
	<view class="message">
		<view class="message-logo">
			<image src="https://boshi.channce.com/imgs/yxstatic/icon/message1.png" mode="widthFix" />
		</view>
		<swiper class="swiper" :autoplay="true" :vertical="true" :interval="4000" :circular="true" :indicator-dots="false">
			<swiper-item v-for="item in list" :key="item.index" @click="navDetial(item)">
				<view class="swiper-item">{{item.title}}</view>
			</swiper-item>
		</swiper>
		<view class="pageMessage-icon" @click="navTo('/pages/notice/notice')">更多</view>
		<!-- <uni-icons class="pageMessage-icon" type="more-filled" size="20" color="#d6d7db" @click="navTo('/pages/notice/notice')"></uni-icons> -->
	</view>
</template>

<script>
	export default {
		name: "notice",
		props: {
			list: {
				type: Array
			},
		},
		created() {
			console.log(this.list);
		},
		data() {
			return {

			};
		},
		methods: {
			navTo(item) {
				uni.navigateTo({ url: item });
			},
			navDetial(item) {
				uni.navigateTo({ url: `/pages/notice/noticeDetail?id=${item.id}&messageType=1` });
			}
		}
	}
</script>

<style lang="scss" scoped>
	// 功能板块-公告
	.message {
		height: 56rpx;
		width: 95vw;
		margin: 30rpx auto;
		border-radius: 10rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #ECF8FF;
		border-radius: 28px;
		padding-right: 20rpx;
		position: relative;



		.message-logo {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			position: absolute;

			image {
				width: 90rpx;
			}

		}

		.swiper {
			height: 100%;
			flex: 1;
			padding-left: 100rpx;

			.swiper-item {
				height: 100%;
				display: flex;
				align-items: center;
				font-size: 24rpx;
				margin-left: 14rpx;
				flex: 1;
				color: #001A8D;
			}

		}

		.pageMessage-icon {
			font-size: 26rpx;
			color: #56638d;
		}
	}
</style>