<template>
  <view class="publish" :style="statusBarStyle">
    <HeaderBack title="发布"></HeaderBack>
    <view class="publish-content">
      <uni-file-picker
        v-model="imageList"
        file-mediatype="image"
        mode="grid"
        :limit="9"
        title="上传图片"
      ></uni-file-picker>
      <view class="title">
        <input type="text" placeholder="填写标题会有更多人浏览" />
      </view>
      <view class="content">
        <textarea
          name=""
          id=""
          placeholder="分享你的玩乐发现，玩了什么，体验如何～"
          placeholder-style="color: #C0C0C0;font-size: 28rpx;"
        ></textarea>
      </view>
      <scroll-view class="topic" scroll-x="true" show-scrollbar="false">
        <view class="topic-container">
          <view v-for="index in 10" :key="index" class="topic-item"
            >#话题</view
          >
        </view>
      </scroll-view>
      <uni-list class="list" :border="false">
        <uni-list-item
          :show-extra-icon="true"
          showArrow
          :extra-icon="{
            type: 'location-filled',
            color: '#2c2c2c',
            size: '22',
          }"
          title="标记地点"
        />
        <scroll-view
          class="topic"
          scroll-x="true"
          show-scrollbar="false"
          style="border: none"
        >
          <view class="topic-container">
            <view v-for="index in 10" :key="index" class="topic-item"
              >#话题
            </view>
          </view>
        </scroll-view>
        <uni-list-item
          :show-extra-icon="true"
          showArrow
          :extra-icon="{
            type: 'shop',
            color: '#2c2c2c',
            size: '22',
          }"
          title="添加商品"
        />
      </uni-list>
    </view>
    <view class="publish-updates">发布动态</view>
  </view>
</template>

<script>
import HeaderBack from "@/components/common/headerBack/HeaderBack.vue";
import statusBarMixin from "@/mixins/statusBarMixin.js";

export default {
  mixins: [statusBarMixin],
  components: {
    HeaderBack,
  },
  data() {
    return {
      imageList: []
    };
  },
};
</script>

<style scoped lang="scss">
.publish {
  position: relative;
  padding: 0 32rpx;
  .publish-content {
    .title {
      padding: 20rpx 0;
      margin-top: 30rpx;
      input {
        width: 100%;
      }
      border-bottom: 1px solid #f4f4f4;
    }
    .content {
      margin-top: 30rpx;
      textarea {
        width: 100%;
        height: 300rpx;
      }
    }
    .topic {
      padding-bottom: 20rpx;
      border-bottom: 1px solid #f4f4f4;
      white-space: nowrap;

      .topic-container {
        display: inline-block;
        white-space: nowrap;

        .topic-item {
          display: inline-block;
          font-size: 22rpx;
          padding: 10rpx 24rpx;
          background-color: #f7f7f7;
          color: #8f8f8f;
          border-radius: 40rpx;
          margin-right: 20rpx;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
  .publish-updates {
    position: fixed;
    bottom: 30rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 686rpx;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #6754ff;
    border-radius: 44rpx;
    color: #fff;
    text-align: center;
  }
}
::v-deep .uni-list-item__container {
  padding-left: 0 !important;
}
::v-deep .uni-list-item__content-title {
  font-weight: bold;
}
::v-deep .uni-list-item__icon {
  margin-right: 0 !important;
}
</style>
