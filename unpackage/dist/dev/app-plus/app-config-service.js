
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/index/index","pages/talentList/talentList","pages/publish/publish","pages/login/login","pages/social/social","pages/mine/mine","pages/mall/mall"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"uni-app","navigationBarBackgroundColor":"#F8F8F8","backgroundColor":"#F8F8F8"},"tabBar":{"color":"#CFCFCF","selectedColor":"#8147FF","borderStyle":"black","backgroundColor":"#F8F8F8","midButton":{"iconPath":"static/tabBar/<EMAIL>","width":"70px","height":"50px","iconWidth":"50px"},"list":[{"pagePath":"pages/index/index","text":"首页","iconPath":"static/tabBar/<EMAIL>","selectedIconPath":"static/tabBar/<EMAIL>"},{"pagePath":"pages/talentList/talentList","text":"论坛","iconPath":"static/tabBar/<EMAIL>","selectedIconPath":"static/tabBar/<EMAIL>"},{"pagePath":"pages/mall/mall","text":"商城","iconPath":"static/tabBar/<EMAIL>","selectedIconPath":"static/tabBar/<EMAIL>"},{"pagePath":"pages/mine/mine","text":"我的","iconPath":"static/tabBar/<EMAIL>","selectedIconPath":"static/tabBar/<EMAIL>"}]},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":false,"autoclose":true},"appname":"铂时","compilerVersion":"4.75","entryPagePath":"pages/index/index","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"首页","navigationStyle":"custom"}},{"path":"/pages/talentList/talentList","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"达人列表","navigationStyle":"custom"}},{"path":"/pages/publish/publish","meta":{},"window":{"navigationBarTitleText":"发布","navigationStyle":"custom"}},{"path":"/pages/login/login","meta":{},"window":{"navigationBarTitleText":"登录","navigationStyle":"custom"}},{"path":"/pages/social/social","meta":{},"window":{"navigationBarTitleText":"社交","navigationStyle":"custom"}},{"path":"/pages/mine/mine","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"我的","navigationStyle":"custom"}},{"path":"/pages/mall/mall","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"商城","navigationStyle":"custom"}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
